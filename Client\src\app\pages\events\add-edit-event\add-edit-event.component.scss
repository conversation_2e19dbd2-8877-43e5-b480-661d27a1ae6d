.event-form-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px 15px;
}

// Loading overlay styles
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(3px);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}

.event-form {
  // .card {
  //   transition: transform 0.2s;
  //   border-radius: 8px;

  //   &:hover {
  //     transform: translateY(-2px);
  //   }
  // }

  .form-label {
    font-weight: 500;
    color: #333;
  }

  .text-danger {
    color: #dc3545 !important;
  }

  .upload-container {
    background-color: #f8f9fa;
    border-radius: 6px;
    transition: all 0.3s ease;

    &:hover {
      background-color: #e9ecef;
    }
  }

  .image-preview img {
    object-fit: cover;
    border-radius: 4px;
  }

  .form-control,
  .form-select {
    border-color: #ced4da;

    &:focus {
      border-color: #dc3545;
      box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
    }
  }

  .form-check-input:checked {
    background-color: #dc3545;
    border-color: #dc3545;
  }

  .btn-outline-primary {
    color: #dc3545;
    border-color: #dc3545;

    &:hover,
    &:active,
    &.active {
      background-color: #dc3545;
      border-color: #dc3545;
      color: white;
    }
  }

  .btn-check:checked + .btn-outline-primary {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
  }

  // Location tabs styling
  .location-tabs {
    .tab-container {
      display: flex;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      overflow: hidden;
      width: 100%;
      max-width: 400px;

      .tab-item {
        flex: 1;
        text-align: center;
        padding: 12px;
        cursor: pointer;
        background-color: #fff;
        transition:
          background-color 0.2s,
          color 0.2s;
        font-size: 14px;

        &:not(:last-child) {
          border-right: 1px solid #dee2e6;
        }

        &.active {
          background-color: #f0f0f0;
          font-weight: 500;
          color: #333;
        }

        &:hover:not(.active) {
          background-color: #f8f9fa;
        }
      }
    }
  }
}

// PrimeNG component styling
:host ::ng-deep {
  // SelectButton styling for location type
  .p-selectbutton {
    .p-button {
      background-color: #fff;
      color: #6c757d;
      border: 1px solid #ced4da;
      transition:
        background-color 0.2s,
        color 0.2s,
        border-color 0.2s;

      &:not(:last-child) {
        border-right: 0;
      }

      &.p-highlight {
        background-color: #dc3545;
        color: #fff;
        border-color: #dc3545;
      }

      &:hover:not(.p-highlight) {
        background-color: #f8f9fa;
      }
    }
  }

  // Calendar styling for date picker triggers
  .p-calendar {
    .p-datepicker-trigger {
      background-color: #dc3545;
      border-color: #dc3545;

      &:hover {
        background-color: #bb2d3b;
        border-color: #bb2d3b;
      }
    }
  }

  .p-datepicker {
    .p-datepicker-header {
      .p-datepicker-title {
        .p-datepicker-month,
        .p-datepicker-year {
          color: #dc3545;
        }
      }
    }

    .p-datepicker-calendar {
      td > span.p-highlight {
        background-color: #dc3545;
      }
    }
  }

  // Fix dropdown width issues
  .p-dropdown {
    width: 100% !important;

    .p-dropdown-label {
      padding: 0.375rem 0.75rem;
      font-size: 1rem;
    }

    .p-dropdown-trigger {
      width: 3rem;
    }

    .p-dropdown-panel {
      width: 100%;
    }
    .p-select-label {
      padding-top: 5px;
      padding-left: 8px;
    }
  }

  // SelectButton styling for location type
  .p-selectbutton {
    .p-button {
      background-color: #fff;
      color: #6c757d;
      border: 1px solid #ced4da;
      transition:
        background-color 0.2s,
        color 0.2s,
        border-color 0.2s;

      &:not(:last-child) {
        border-right: 0;
      }

      &.p-highlight {
        background-color: #dc3545;
        color: #fff;
        border-color: #dc3545;
      }

      &:hover:not(.p-highlight) {
        background-color: #f8f9fa;
      }
    }
  }

  // Calendar styling
  .p-calendar {
    width: 100%;

    .p-inputtext {
      width: 100%;
      padding: 0.375rem 0.75rem;
      font-size: 1rem;
      border: 1px solid #ced4da;
      border-radius: 0.25rem;

      &:focus {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
      }
    }

    .p-datepicker-trigger {
      background-color: #dc3545;
      border-color: #dc3545;

      &:hover {
        background-color: #bb2d3b;
        border-color: #bb2d3b;
      }
    }
  }

  .p-datepicker {
    .p-datepicker-header {
      .p-datepicker-title {
        .p-datepicker-month,
        .p-datepicker-year {
          color: #dc3545;
        }
      }
    }

    .p-datepicker-calendar {
      td > span.p-highlight {
        background-color: #dc3545;
      }
    }
  }
}
