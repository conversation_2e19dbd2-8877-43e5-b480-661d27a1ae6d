<!-- Toast for notifications -->
<p-toast position="top-right" styleClass="custom-toast"></p-toast>

<!-- Loading Overlay for form submission -->
<div *ngIf="isSubmitting" class="loading-overlay">
  <div class="loading-content">
    <p-progressSpinner
      strokeWidth="4"
      [style]="{ width: '50px', height: '50px' }"
    ></p-progressSpinner>
    <div class="mt-3 text-white">Processing your request...</div>
  </div>
</div>

<!-- Loading Spinner for initial load -->
<div
  *ngIf="isLoading"
  class="d-flex justify-content-center align-items-center"
  style="min-height: 400px"
>
  <p-progressSpinner
    strokeWidth="4"
    [style]="{ width: '50px', height: '50px' }"
  ></p-progressSpinner>
</div>

<!-- Form Content -->
<div *ngIf="!isLoading" class="resource-form-container">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <div class="d-flex align-items-center">
      <a (click)="cancel()" class="back-button">
        <i class="pi pi-arrow-left"></i>
        <h2 class="mb-0">{{ isEditMode ? "Edit" : "Create" }} Resource</h2>
      </a>
    </div>
  </div>

  <form
    [formGroup]="resourceForm"
    (ngSubmit)="onSubmit()"
    class="needs-validation compact-form"
  >
    <!-- Error Alert for Duplicate Title -->
    <div
      *ngIf="
        resourceForm
          .get('basicInfo.organizationTitle')
          ?.hasError('duplicateTitle')
      "
      class="alert alert-danger mb-4 p-3"
      role="alert"
      style="
        font-size: 1.1rem;
        border-left: 5px solid #dc3545;
        font-weight: 500;
      "
    >
      <i class="bi bi-exclamation-triangle-fill me-2"></i>
      <strong style="font-size: 1.2rem">Duplicate Title Error:</strong>
      <div class="mt-2">
        A resource with this title already exists. Please use a different title.
      </div>
    </div>

    <!-- Social Media Validation Errors -->
    <div
      *ngIf="socialMediaErrors.length > 0"
      class="alert alert-danger mb-4 p-3"
      role="alert"
      style="
        font-size: 1.1rem;
        border-left: 5px solid #dc3545;
        font-weight: 500;
      "
    >
      <i class="bi bi-exclamation-triangle-fill me-2"></i>
      <strong style="font-size: 1.2rem">Social Media URL Format Errors:</strong>
      <ul class="mt-2 mb-0 ps-3">
        <li *ngFor="let error of socialMediaErrors">{{ error }}</li>
      </ul>
    </div>

    <!-- Basic Info Section -->
    <div class="card mb-4 border-0 shadow-sm" formGroupName="basicInfo">
      <div class="card-body">
        <h4 class="mb-2">Basic Info</h4>

        <!-- Image Upload Row -->
        <div class="row mb-4">
          <!-- Resource Image Upload -->
          <div class="col-md-6 mb-3">
            <div class="upload-container p-3 border rounded">
              <div class="d-flex align-items-center mb-2">
                <i class="bi bi-cloud-arrow-up text-primary me-2"></i>
                <label class="form-label mb-0"
                  >Upload Image of Resource<span class="text-danger"
                    >*</span
                  ></label
                >
              </div>
              <p class="text-muted small mb-3">
                (Select jpg, png & pdf files, upload max-1 image)
              </p>

              <div class="d-flex align-items-center">
                <div class="image-preview me-3" *ngIf="resourceImagePreview">
                  <img
                    [src]="
                      resourceImagePreview.startsWith('data:')
                        ? resourceImagePreview
                        : getFullImagePath(resourceImagePreview)
                    "
                    alt="Resource Image"
                    class="img-thumbnail"
                    style="max-width: 100px; max-height: 100px"
                  />
                </div>
                <div class="upload-btn-wrapper">
                  <input
                    type="file"
                    class="form-control"
                    accept=".jpg,.jpeg,.png,.pdf"
                    (change)="onResourceImageSelected($event)"
                    [class.is-invalid]="
                      !resourceImage && !resourceImagePreview && formSubmitted
                    "
                    required
                  />
                  <div class="invalid-feedback">Resource image is required</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Resource Logo Upload -->
          <div class="col-md-6 mb-3">
            <div class="upload-container p-3 border rounded">
              <div class="d-flex align-items-center mb-2">
                <i class="bi bi-cloud-arrow-up text-primary me-2"></i>
                <label class="form-label mb-0"
                  >Upload Resource Logo<span class="text-danger">*</span></label
                >
              </div>
              <p class="text-muted small mb-3">
                (Minimum 300 x 300 size required. Select jpg, png & pdf files)
              </p>

              <div class="d-flex align-items-center">
                <div class="image-preview me-3" *ngIf="resourceLogoPreview">
                  <img
                    [src]="
                      resourceLogoPreview.startsWith('data:')
                        ? resourceLogoPreview
                        : getFullImagePath(resourceLogoPreview)
                    "
                    alt="Resource Logo"
                    class="img-thumbnail"
                    style="max-width: 100px; max-height: 100px"
                  />
                </div>
                <div class="upload-btn-wrapper">
                  <input
                    type="file"
                    class="form-control"
                    accept=".jpg,.jpeg,.png,.pdf"
                    (change)="onResourceLogoSelected($event)"
                    [class.is-invalid]="
                      !resourceLogo && !resourceLogoPreview && formSubmitted
                    "
                    required
                  />
                  <div class="invalid-feedback">Resource logo is required</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Organization Title -->
        <div class="mb-3">
          <label for="organizationTitle" class="form-label"
            >Organization Title<span class="text-danger">*</span></label
          >
          <input
            pInputText
            type="text"
            class="w-100"
            id="organizationTitle"
            formControlName="organizationTitle"
            [ngClass]="{
              'ng-invalid ng-dirty':
                resourceForm.get('basicInfo.organizationTitle')?.invalid &&
                resourceForm.get('basicInfo.organizationTitle')?.touched,
            }"
            (input)="onTitleChange()"
          />
          <div
            class="text-danger small mt-1"
            *ngIf="
              resourceForm.get('basicInfo.organizationTitle')?.invalid &&
              resourceForm.get('basicInfo.organizationTitle')?.touched
            "
          >
            <span
              *ngIf="
                resourceForm.get('basicInfo.organizationTitle')?.errors?.[
                  'required'
                ]
              "
            >
              Organization title is required
            </span>
            <span
              *ngIf="
                resourceForm.get('basicInfo.organizationTitle')?.errors?.[
                  'duplicateTitle'
                ]
              "
            >
              A resource with this title already exists. Please use a different
              title.
            </span>
          </div>
        </div>

        <!-- Sub Title and Resource Category -->
        <div class="row">
          <div class="col-md-6 mb-3">
            <label for="subTitle" class="form-label">Sub Title</label>
            <input
              pInputText
              type="text"
              class="w-100"
              id="subTitle"
              formControlName="subTitle"
            />
          </div>
          <div class="col-md-6 mb-3">
            <label for="resourceCategory" class="form-label"
              >Resource Category<span class="text-danger">*</span></label
            >
            <p-dropdown
              id="resourceCategory"
              formControlName="resourceCategory"
              [options]="resourceCategoryOptions"
              placeholder="Select a category"
              styleClass="w-100"
              [ngClass]="{
                'ng-invalid ng-dirty':
                  resourceForm.get('basicInfo.resourceCategory')?.invalid &&
                  resourceForm.get('basicInfo.resourceCategory')?.touched,
              }"
            ></p-dropdown>
            <div
              class="text-danger small mt-1"
              *ngIf="
                resourceForm.get('basicInfo.resourceCategory')?.invalid &&
                resourceForm.get('basicInfo.resourceCategory')?.touched
              "
            >
              Resource category is required
            </div>
          </div>
        </div>

        <!-- Address and City -->
        <div class="row">
          <div class="col-md-6 mb-3">
            <label for="address" class="form-label"
              >Address<span class="text-danger">*</span></label
            >
            <input
              pInputText
              type="text"
              class="w-100"
              id="address"
              formControlName="address"
              [ngClass]="{
                'ng-invalid ng-dirty':
                  resourceForm.get('basicInfo.address')?.invalid &&
                  resourceForm.get('basicInfo.address')?.touched,
              }"
            />
            <div
              class="text-danger small mt-1"
              *ngIf="
                resourceForm.get('basicInfo.address')?.invalid &&
                resourceForm.get('basicInfo.address')?.touched
              "
            >
              Address is required
            </div>
          </div>
          <div class="col-md-6 mb-3">
            <label for="city" class="form-label"
              >City<span class="text-danger">*</span></label
            >
            <input
              pInputText
              type="text"
              class="w-100"
              id="city"
              formControlName="city"
              [ngClass]="{
                'ng-invalid ng-dirty':
                  resourceForm.get('basicInfo.city')?.invalid &&
                  resourceForm.get('basicInfo.city')?.touched,
              }"
            />
            <div
              class="text-danger small mt-1"
              *ngIf="
                resourceForm.get('basicInfo.city')?.invalid &&
                resourceForm.get('basicInfo.city')?.touched
              "
            >
              City is required
            </div>
          </div>
        </div>

        <!-- State and Zip Code -->
        <div class="row">
          <div class="col-md-6 mb-3">
            <label for="state" class="form-label"
              >State<span class="text-danger">*</span></label
            >
            <input
              pInputText
              type="text"
              class="w-100"
              id="state"
              formControlName="state"
              [ngClass]="{
                'ng-invalid ng-dirty':
                  resourceForm.get('basicInfo.state')?.invalid &&
                  resourceForm.get('basicInfo.state')?.touched,
              }"
            />
            <div
              class="text-danger small mt-1"
              *ngIf="
                resourceForm.get('basicInfo.state')?.invalid &&
                resourceForm.get('basicInfo.state')?.touched
              "
            >
              State is required
            </div>
          </div>
          <div class="col-md-6 mb-3">
            <label for="zipCode" class="form-label"
              >Zip code<span class="text-danger">*</span></label
            >
            <input
              pInputText
              type="text"
              class="w-100"
              id="zipCode"
              formControlName="zipCode"
              [ngClass]="{
                'ng-invalid ng-dirty':
                  resourceForm.get('basicInfo.zipCode')?.invalid &&
                  resourceForm.get('basicInfo.zipCode')?.touched,
              }"
            />
            <div
              class="text-danger small mt-1"
              *ngIf="
                resourceForm.get('basicInfo.zipCode')?.invalid &&
                resourceForm.get('basicInfo.zipCode')?.touched
              "
            >
              Zip code is required
            </div>
          </div>
        </div>

        <!-- Short Description -->
        <div class="mb-3">
          <label for="shortDescription" class="form-label"
            >Resource Short Description<span class="text-danger">*</span></label
          >
          <textarea
            pTextarea
            class="w-100"
            id="shortDescription"
            rows="3"
            formControlName="shortDescription"
            [ngClass]="{
              'ng-invalid ng-dirty':
                resourceForm.get('basicInfo.shortDescription')?.invalid &&
                resourceForm.get('basicInfo.shortDescription')?.touched,
            }"
          ></textarea>
          <div
            class="text-danger small mt-1"
            *ngIf="
              resourceForm.get('basicInfo.shortDescription')?.invalid &&
              resourceForm.get('basicInfo.shortDescription')?.touched
            "
          >
            Short description is required
          </div>
        </div>

        <!-- Services -->
        <div class="mb-4">
          <label class="form-label">
            Services<span class="text-danger">*</span>
            <small class="text-muted">(min-1 service required)</small>
          </label>
          <div>
            <div
              *ngFor="let service of servicesArray.controls; let i = index"
              class="mb-3"
            >
              <div class="d-flex align-items-center">
                <div class="flex-grow-1 position-relative">
                  <input
                    pInputText
                    type="text"
                    class="w-100"
                    [(ngModel)]="service.value"
                    [ngModelOptions]="{ standalone: true }"
                    placeholder="Enter service"
                  />
                  <button
                    *ngIf="servicesArray.length > 1"
                    type="button"
                    class="btn btn-link position-absolute end-0 top-50 translate-middle-y p-0"
                    (click)="removeService(i)"
                  >
                    <i class="bi bi-trash text-danger"></i>
                  </button>
                </div>
              </div>
            </div>
            <button
              type="button"
              class="btn btn-danger rounded-pill mt-3"
              (click)="addService()"
            >
              <i class="bi bi-plus-circle me-1"></i> Add Service
            </button>
          </div>
        </div>

        <!-- Long Description -->
        <div class="mb-3">
          <label for="longDescription" class="form-label"
            >Resource Long Description<span class="text-danger">*</span></label
          >
          <textarea
            pTextarea
            class="w-100"
            id="longDescription"
            rows="5"
            formControlName="longDescription"
            [ngClass]="{
              'ng-invalid ng-dirty':
                resourceForm.get('basicInfo.longDescription')?.invalid &&
                resourceForm.get('basicInfo.longDescription')?.touched,
            }"
          ></textarea>
          <div
            class="text-danger small mt-1"
            *ngIf="
              resourceForm.get('basicInfo.longDescription')?.invalid &&
              resourceForm.get('basicInfo.longDescription')?.touched
            "
          >
            Long description is required
          </div>
        </div>

        <!-- Resource Type -->
        <div class="mb-3">
          <label class="form-label d-block">Choose Resource Type</label>
          <div class="d-flex flex-wrap gap-3 mt-2">
            <div class="d-flex align-items-center">
              <p-radioButton
                inputId="externalPartner"
                [value]="resourceTypes.ExternalPartner"
                formControlName="type"
              ></p-radioButton>
              <label for="externalPartner" class="ms-2 mb-0"
                >External Partner</label
              >
            </div>
            <div class="d-flex align-items-center">
              <p-radioButton
                inputId="southWardPromise"
                [value]="resourceTypes.SouthWardPromiseNeighbourhood"
                formControlName="type"
              ></p-radioButton>
              <label for="southWardPromise" class="ms-2 mb-0"
                >South Ward Promise Neighbourhood</label
              >
            </div>
            <div class="d-flex align-items-center">
              <p-radioButton
                inputId="swpnPartner"
                [value]="resourceTypes.SWPNPartner"
                formControlName="type"
              ></p-radioButton>
              <label for="swpnPartner" class="ms-2 mb-0">SWPN Partner</label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Contact Details Section -->
    <div class="card mb-4 border-0 shadow-sm" formGroupName="contactDetails">
      <div class="card-body">
        <h4 class="mb-4">Contact Details</h4>

        <div class="row">
          <!-- Contact Name -->
          <div class="col-md-6 mb-3">
            <label for="contactName" class="form-label">Contact Name</label>
            <input
              pInputText
              type="text"
              class="w-100"
              id="contactName"
              placeholder="John Doe"
              formControlName="contactName"
              [ngClass]="{
                'ng-invalid ng-dirty':
                  resourceForm.get('contactDetails.contactName')?.invalid &&
                  resourceForm.get('contactDetails.contactName')?.touched,
              }"
            />
            <div
              class="text-danger small mt-1"
              *ngIf="
                resourceForm.get('contactDetails.contactName')?.invalid &&
                resourceForm.get('contactDetails.contactName')?.touched
              "
            >
              Contact name is required
            </div>
          </div>

          <!-- Contact Number -->
          <div class="col-md-6 mb-3">
            <label for="contactNo" class="form-label">Contact No</label>
            <input
              pInputText
              type="text"
              class="w-100"
              id="contactNo"
              placeholder="10-digit phone number"
              pattern="^[0-9]{10}$"
              formControlName="contactNo"
              [ngClass]="{
                'ng-invalid ng-dirty':
                  resourceForm.get('contactDetails.contactNo')?.invalid &&
                  resourceForm.get('contactDetails.contactNo')?.touched,
              }"
            />
            <div
              class="text-danger small mt-1"
              *ngIf="
                resourceForm.get('contactDetails.contactNo')?.invalid &&
                resourceForm.get('contactDetails.contactNo')?.touched
              "
            >
              Valid Contact number is required
            </div>
          </div>
        </div>

        <div class="row">
          <!-- Website -->
          <div class="col-md-6 mb-3">
            <label for="website" class="form-label">Website</label>
            <input
              pInputText
              type="url"
              class="w-100"
              placeholder="https://www.example.com"
              pattern="(https?:\/\/)?(www\.)?[a-zA-Z0-9-]+(\.[a-zA-Z]{2,})+\/?.*"
              id="website"
              formControlName="website"
              [ngClass]="{
                'ng-invalid ng-dirty':
                  resourceForm.get('contactDetails.website')?.invalid &&
                  resourceForm.get('contactDetails.website')?.touched,
              }"
            />
            <div
              class="text-danger small mt-1"
              *ngIf="
                resourceForm.get('contactDetails.website')?.errors?.[
                  'pattern'
                ] && resourceForm.get('contactDetails.website')?.touched
              "
            >
              Valid website URL is required
            </div>
          </div>

          <!-- Email -->
          <div class="col-md-6 mb-3">
            <label for="email" class="form-label">Email</label>
            <input
              pInputText
              type="email"
              class="w-100"
              placeholder="<EMAIL>"
              id="email"
              formControlName="email"
              [ngClass]="{
                'ng-invalid ng-dirty':
                  resourceForm.get('contactDetails.email')?.invalid &&
                  resourceForm.get('contactDetails.email')?.touched,
              }"
            />
            <div
              class="text-danger small mt-1"
              *ngIf="
                resourceForm.get('contactDetails.email')?.invalid &&
                resourceForm.get('contactDetails.email')?.touched
              "
            >
              Valid email is required
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Social Media Section -->
    <div class="card mb-4 border-0 shadow-sm" formGroupName="socialMedia">
      <div class="card-body">
        <h4 class="mb-4">Social Media</h4>

        <div class="row">
          <!-- Facebook -->
          <div class="col-md-6 mb-3">
            <label for="facebook" class="form-label">Facebook</label>

            <div class="input-group">
              <span class="input-group-text"
                ><i class="bi bi-facebook"></i
              ></span>
              <input
                pInputText
                type="url"
                class="w-100"
                id="facebook"
                formControlName="facebook"
                pattern="(https?:\/\/)?(www\.)?facebook\.com\/[a-zA-Z0-9\.]+"
                placeholder="facebook.com/yourpage "
                [ngClass]="{
                  'ng-invalid ng-dirty':
                    resourceForm.get('socialMedia.facebook')?.invalid &&
                    resourceForm.get('socialMedia.facebook')?.touched,
                }"
              />
            </div>
            <div
              class="text-danger mt-1"
              *ngIf="
                resourceForm.get('socialMedia.facebook')?.errors?.['pattern'] &&
                resourceForm.get('socialMedia.facebook')?.touched
              "
            >
              <i class="bi bi-exclamation-circle me-1"></i>
              Enter valid URL format (Example: facebook.com/yourpage)
            </div>
          </div>

          <!-- Instagram -->
          <div class="col-md-6 mb-3">
            <label for="instagram" class="form-label">Instagram</label>

            <div class="input-group">
              <span class="input-group-text"
                ><i class="bi bi-instagram"></i
              ></span>
              <input
                pInputText
                type="url"
                class="w-100"
                id="instagram"
                formControlName="instagram"
                pattern="(https?:\/\/)?(www\.)?instagram\.com\/[A-Za-z0-9_]{1,30}"
                placeholder="instagram.com/yourhandle"
                [ngClass]="{
                  'ng-invalid ng-dirty':
                    resourceForm.get('socialMedia.instagram')?.invalid &&
                    resourceForm.get('socialMedia.instagram')?.touched,
                }"
              />
            </div>
            <div
              class="text-danger mt-1"
              *ngIf="
                resourceForm.get('socialMedia.instagram')?.errors?.[
                  'pattern'
                ] && resourceForm.get('socialMedia.instagram')?.touched
              "
            >
              <i class="bi bi-exclamation-circle me-1"></i>
              Enter valid URL format (Example: instagram.com/yourhandle)
            </div>
          </div>
        </div>

        <div class="row">
          <!-- Twitter -->
          <div class="col-md-6 mb-3">
            <label for="twitter" class="form-label">Twitter (X)</label>

            <div class="input-group">
              <span class="input-group-text"
                ><i class="pi pi-twitter"></i
              ></span>
              <input
                pInputText
                type="url"
                class="w-100"
                id="twitter"
                formControlName="twitter"
                pattern="(https?:\/\/)?(www\.)?(twitter\.com|x\.com)\/[A-Za-z0-9_]{1,15}"
                placeholder="twitter.com/yourhandle"
                [ngClass]="{
                  'ng-invalid ng-dirty':
                    resourceForm.get('socialMedia.twitter')?.invalid &&
                    resourceForm.get('socialMedia.twitter')?.touched,
                }"
              />
            </div>
            <div
              class="text-danger mt-1"
              *ngIf="
                resourceForm.get('socialMedia.twitter')?.errors?.['pattern'] &&
                resourceForm.get('socialMedia.twitter')?.touched
              "
            >
              <i class="bi bi-exclamation-circle me-1"></i>
              Enter valid URL format (Example: twitter.com/yourhandle)
            </div>
          </div>

          <!-- LinkedIn -->
          <div class="col-md-6 mb-3">
            <label for="linkedIn" class="form-label">LinkedIn</label>

            <div class="input-group">
              <span class="input-group-text"
                ><i class="bi bi-linkedin"></i
              ></span>
              <input
                pInputText
                type="url"
                class="w-100"
                id="linkedIn"
                formControlName="linkedIn"
                pattern="(https?:\/\/)?(www\.)?linkedin\.com\/in\/[A-Za-z0-9_-]{5,30}"
                placeholder="linkedin.com/in/yourprofile"
                [ngClass]="{
                  'ng-invalid ng-dirty':
                    resourceForm.get('socialMedia.linkedIn')?.invalid &&
                    resourceForm.get('socialMedia.linkedIn')?.touched,
                }"
              />
            </div>
            <div
              class="text-danger mt-1"
              *ngIf="
                resourceForm.get('socialMedia.linkedIn')?.errors?.['pattern'] &&
                resourceForm.get('socialMedia.linkedIn')?.touched
              "
            >
              <i class="bi bi-exclamation-circle me-1"></i>
              Enter valid URL format (Example: linkedin.com/in/yourprofile)
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="d-flex justify-content-end mb-4">
      <button
        type="button"
        class="btn btn-outline-secondary me-2"
        (click)="cancel()"
      >
        Cancel
      </button>
      <button type="submit" class="btn btn-danger" [disabled]="isSubmitting">
        <span
          *ngIf="isSubmitting"
          class="spinner-border spinner-border-sm me-1"
          role="status"
          aria-hidden="true"
        ></span>
        {{ isEditMode ? "Update" : "Create" }} Resource
      </button>
    </div>
  </form>
</div>
